from typing import Dict, List, Tuple
import pandas as pd
from tqdm import tqdm  # For progress bars
import difflib  # 用于模糊匹配
from functools import lru_cache
from concurrent.futures import ThreadPoolExecutor
import multiprocessing
import warnings
warnings.filterwarnings('ignore')


def load_data_files() -> Dict[str, pd.DataFrame]:
    """Load all required Excel files"""
    print("\n正在加载Excel文件...")
    files = {
        'recruit_plan_2022': './new_data_recruit_plan/41_0_2022_44_get_recruit_plan.xlsx',
        'recruit_plan_2023': './new_data_recruit_plan/41_0_2023_44_get_recruit_plan.xlsx',
        'recruit_plan_2024': './new_data_recruit_plan/41_0_2024_44_get_recruit_plan.xlsx',
        # 'recruit_plan_2025': "./new_data_recruit_plan/41_0_2025_44_get_recruit_plan.xlsx",
        'recruit_plan_2025': "./1.xlsx",
        'score_2024': "./new_data/41_0_2024_44_get_major_score_line.xlsx",
        'score_2023': "./new_data/41_0_2023_44_get_major_score_line.xlsx",
        'score_2022': "./new_data/41_0_2022_44_get_major_score_line.xlsx"
    }
    
    # 使用线程池并行加载文件
    with ThreadPoolExecutor() as executor:
        futures = {name: executor.submit(pd.read_excel, path) 
                  for name, path in files.items()}
        return {name: future.result() for name, future in futures.items()}

def filter_by_type(dataframes: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
    """Filter dataframes by type_id"""
    print("\n正在过滤数据...")

    # 2
    # type_ids = ['3_46_0', '3_1570_0', '3_1939_0', '4_8_0', '3_16_0', '3_17_0']

    # 1
    # type_ids = ['1_10_0', '2073_10_0']
    type_ids = ['2073_14_0','1_8_0', '1_7_0', '1_51_0', '1_44_0', '2073_3453_0', '1_150_0', '1_111_0', '2073_46_0', '2073_47_0',  '2073_14_0']

    # 0
    # type_ids = ['2_10_0', '2074_10_0']
    # type_ids = ['2074_14_0','2_8_0', '2_7_0', '2_51_0', '2_44_0', '2074_3453_0', '2_150_0', '2_111_0', '2074_46_0', '2074_47_0',  '2074_14_0']


    filtered = {}
    for name, df in dataframes.items():
        filtered[name] = df[df['type_id'].isin(type_ids)].copy()
        filtered[name]['spname_copy'] = filtered[name]['spname']
        # 预处理数据
        if 'spname' in df.columns:
            filtered[name]['spname'] = (df['spname'].str.replace('（', '(')
                                      .str.replace('）', ')')
                                      .str.split('(').str[0])
    return filtered

def clean_text(df: pd.DataFrame) -> pd.DataFrame:
    """Clean text in DataFrame columns"""
    print("\n正在清理文本数据...")
    text_columns = ['sg_name', 'spname', 'sp_info', 'sg_info']

    for col in tqdm(text_columns, desc="清理列"):
        if col in df.columns:
            try:
                if col == 'spname':
                    df[col] = df[col].str.replace('（', '(').str.replace('）', ')')
                else:
                    df[col] = df[col].str.replace('（', '').str.replace('）', '')
                    if col in ['sp_info', 'sg_info']:
                        df[col] = df[col].str.replace('首选物理，再选', '').str.replace('首选历史，再选', '')
            except AttributeError:
                continue
    return df

def convert_to_dict(row) -> Dict:
    """将DataFrame行转换为字典，处理numpy类型"""
    result = {}
    for key, value in row.items():
        if pd.api.types.is_integer_dtype(type(value)):
            result[key] = str(value)
        else:
            result[key] = value
    return result

@lru_cache(maxsize=1024)
def get_similarity(a: str, b: str) -> float:
    """计算两个字符串的相似度，使用缓存避免重复计算"""
    if pd.isna(a) or pd.isna(b):
        return 0.0
    return difflib.SequenceMatcher(None, str(a), str(b)).ratio()

school_not_found = ()
def process_data(origin_spname: str, df: pd.DataFrame, sid: str) -> Dict:
    """根据专业名称进行匹配，返回匹配结果"""
    if df.empty:
        return {}
    
    # 替换 id
    if sid in school_dict:
        sid = school_dict[sid]
    else:
        print(f"警告：学校ID {sid} 未在字典中找到，可能导致匹配失败。")
        school_not_found.append(sid)
        return {}
        
    # 过滤特定学校的数据
    school_data = df[df['school_id'] == sid]
    if school_data.empty:
        return {}
    
    # 获取处理后的专业名称
    spname = origin_spname.split('(')[0] if '(' in origin_spname else origin_spname
    
    # 1. 完全匹配
    exact_match = school_data[school_data['spname'] == spname]
    if not exact_match.empty:
        return convert_to_dict(exact_match.iloc[0])
    
    # 2. 模糊匹配 - 使用向量化操作并安全处理索引
    try:
        similarities = school_data['spname'].apply(lambda x: get_similarity(spname, x))
        if not similarities.empty:
            max_similarity = similarities.max()
            if max_similarity >= 0.8:
                best_match_idx = similarities.idxmax()
                return convert_to_dict(school_data.loc[best_match_idx])
    except Exception as e:
        print(f"模糊匹配出错: {str(e)}")
    
    # 3. 包含匹配
    try:
        contained_matches = school_data[
            (school_data['spname'].str.contains(spname, na=False)) |
            (school_data['spname'].apply(lambda x: spname in str(x) if pd.notna(x) else False))
        ]
        
        if not contained_matches.empty:
            return convert_to_dict(contained_matches.iloc[0])
    except Exception as e:
        print(f"包含匹配出错: {str(e)}")
    
    return {}

def process_chunk(args: Tuple) -> List[Dict]:
    """处理数据块"""
    items, processed_data = args
    chunk_results = []
    
    for item in items:
        try:
            result = {
                "school_id": item['school_id'],
                "school_name": item['school_name'],
                "sg_name": item['sg_name'],
                "spname": item['spname'],
                "spname_copy": item['spname_copy'],
                "num": item['num'],
                "tuition": item['tuition'],
                "sg_info": item['sg_info'],
                "sp_info": item['sp_info'],
                "history_data": []
            }
            
            for year, plan_df, score_df in processed_data:
                plan_match = process_data(item['spname'], plan_df, item['school_id'])
                score_match = process_data(item['spname'], score_df, item['school_id'])

                # print(plan_match, score_match)    
                year_data = {
                    "year": year,
                    "plan_num": plan_match.get('num', '-'),
                    "min": score_match.get('min', '-') if score_match else '-',
                    "min_section": score_match.get('min_section', '-') if score_match else '-'
                }
                # print(year_data)
                result["history_data"].append(year_data)
            
            chunk_results.append(result)
        except Exception as e:
            print(f"处理数据时出错: {str(e)}")
            continue
    
    return chunk_results

if __name__ == '__main__':
    # 加载匹配成功的院校id 数据
    school_data = pd.read_excel("getRealID/finish/内蒙古本科匹配成功的数据.xlsx")
    print(school_data.columns)
    # dict 原本的学校id -> 真实学校id
    school_dict = {row['old_sid']: row['sid'] for row in school_data.to_dict('records')}
    # try:
    # 加载数据
    data_files = load_data_files()
    
    # 过滤和预处理数据
    data_files = filter_by_type(data_files)
    
    # 准备2025年数据
    recruit_plan_2025 = data_files['recruit_plan_2025'].to_dict('records')
    
    # 准备历史数据
    processed_data = [
        (2024, data_files['recruit_plan_2024'], data_files['score_2024']),
        (2023, data_files['recruit_plan_2023'], data_files['score_2023']),
        (2022, data_files['recruit_plan_2022'], data_files['score_2022'])
    ]
    
    # 将数据分块处理
    chunk_size = max(len(recruit_plan_2025) // (multiprocessing.cpu_count() * 4), 1)
    chunks = [recruit_plan_2025[i:i + chunk_size] 
            for i in range(0, len(recruit_plan_2025), chunk_size)]
    
    # 使用线程池处理数据
    results = []
    with ThreadPoolExecutor() as executor:
        futures = [executor.submit(process_chunk, (chunk, processed_data)) 
                for chunk in chunks]
        
        for future in tqdm(futures, desc="处理数据"):
            chunk_result = future.result()
            if chunk_result:
                results.extend(chunk_result)
    
    # 保存结果
    if results:
        result_df = pd.DataFrame(results)
        # # 匹配真实学校ID
        # school_df = pd.read_excel(data_files['school_data'])
        # school_dict = {row['id']: row['old_sid'] for row in school_df.to_dict('records')}
        # result_df['school_id'] = result_df['school_id'].map(school_dict)
        result_df['2024_min'] = result_df['history_data'].apply(lambda x: x[0]['min'])
        result_df['2024_min_section'] = result_df['history_data'].apply(lambda x: x[0]['min_section'])
        result_df['2024_num'] = result_df['history_data'].apply(lambda x: x[0]['plan_num'])

        result_df['2023_min'] = result_df['history_data'].apply(lambda x: x[1]['min'])
        result_df['2023_min_section'] = result_df['history_data'].apply(lambda x: x[1]['min_section'])
        result_df['2023_num'] = result_df['history_data'].apply(lambda x: x[1]['plan_num'])

        result_df['2022_min'] = result_df['history_data'].apply(lambda x: x[2]['min'])
        result_df['2022_min_section'] = result_df['history_data'].apply(lambda x: x[2]['min_section'])
        result_df['2022_num'] = result_df['history_data'].apply(lambda x: x[2]['plan_num'])
        result_df.to_excel('processed_results.xlsx', index=False)
        print("处理完成，结果已保存到 processed_results.xlsx")
    else:
        print("警告：没有处理到任何数据")
        
    # except Exception as e:
    #     print(f"程序执行出错: {str(e)}")
    print(f"未找到的学校ID: {school_not_found}")

    
